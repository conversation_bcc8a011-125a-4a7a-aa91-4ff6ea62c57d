defmodule PetalProWeb.NotificationsLiveTest do
  use PetalProWeb.ConnCase

  import PetalPro.AccountsFixtures
  import Phoenix.LiveViewTest

  describe "LiveView stability with unhandled notifications" do
    setup :register_and_sign_in_user

    # test "LiveView remains stable when receiving unhandled notifications_updated broadcast" do
    #   # Define a minimal test-only LiveView module within the test
    #   defmodule TestLiveView do
    #     use Phoenix.LiveView

    #     alias PetalPro.Notifications

    #     @impl true
    #     def mount(_params, _session, socket) do
    #       # Subscribe to user notifications topic to simulate a LiveView that
    #       # might subscribe to notifications but doesn't handle all notification types
    #       user_id = socket.assigns[:user_id] || 123
    #       Phoenix.PubSub.subscribe(PetalPro.PubSub, Notifications.user_notifications_topic(user_id))

    #       {:ok, Phoenix.Component.assign(socket, :test_state, "initial")}
    #     end

    #     @impl true
    #     def render(assigns) do
    #       ~H"""
    #       <div id="test-liveview">
    #         <p>Test State: {@test_state}</p>
    #       </div>
    #       """
    #     end

    #     @impl true
    #     def handle_info(:test_event, socket) do
    #       {:noreply, Phoenix.Component.assign(socket, :test_state, "test_event_handled")}
    #     end

    #     # Intentionally NOT implementing handle_info for Phoenix.Socket.Broadcast messages
    #     # This should demonstrate the unobtrusive behavior - the LiveView should not crash
    #     # when it receives notification broadcasts it doesn't explicitly handle
    #   end

    #   # Set up user for the notification
    #   user = user_fixture()

    #   # Start the test LiveView with user_id in assigns
    #   {:ok, view, _html} = live_isolated(build_conn(), TestLiveView, session: %{"user_id" => user.id})

    #   # Verify initial state
    #   assert render(view) =~ "Test State: initial"

    #   # Get the LiveView process PID
    #   lv_pid = view.pid

    #   # Verify the process is alive before sending the notification
    #   assert Process.alive?(lv_pid)

    #   # Simulate a notification broadcast using the actual notification system
    #   # This is what happens when Notifications.broadcast_user_notification/1 is called
    #   PetalProWeb.Endpoint.broadcast(
    #     PetalPro.Notifications.user_notifications_topic(user.id),
    #     "notifications_updated",
    #     %{id: 123, type: :invited_to_org}
    #   )

    #   # Give the process a moment to handle (or not handle) the broadcast
    #   Process.sleep(50)

    #   # Assert that the LiveView process remains alive and doesn't crash
    #   assert Process.alive?(lv_pid)

    #   # Assert that the LiveView continues to respond to normal operations
    #   # by sending a test event it was designed to handle
    #   send(lv_pid, :test_event)

    #   # Give the process a moment to handle the test event
    #   Process.sleep(10)

    #   # Verify the LiveView still responds correctly to events it handles
    #   assert render(view) =~ "Test State: test_event_handled"

    #   # Verify the process is still alive after handling the test event
    #   assert Process.alive?(lv_pid)

    #   # Additional verification: ensure we can still interact with the LiveView normally
    #   # by checking that it maintains its state and functionality
    #   assert view.pid == lv_pid
    # end

    test "My test" do
      # Define a minimal test-only LiveView module within the test
      defmodule TestLiveView do
        use Phoenix.LiveView

        alias PetalPro.Notifications

        # on_mount(PetalProWeb.UserOnMountHooks, :require_authenticated_user)
        on_mount {PetalProWeb.UserOnMountHooks, :require_authenticated_user}

        @impl true
        def mount(_params, _session, socket) do
          {:ok, socket}
        end

        @impl true
        def render(assigns) do
          ~H"""
          <.layout current_page={:dashboard} current_user={@current_user} type="sidebar">
            <.container class="py-16">
              <.h2>{gettext("Welcome, %{name}", name: user_name(@current_user))} 👋</.h2>

              <div class="px-4 py-8 sm:px-0">
                <div class="flex h-96 items-center justify-center rounded-lg border-4 border-dashed border-gray-300 dark:border-gray-800">
                  <div class="text-xl">Build your masterpiece 🚀</div>
                </div>
              </div>
            </.container>
          </.layout>
          """
        end

        @impl true
        def handle_info(:unused_event, socket) do
          {:noreply, socket}
        end

        # Intentionally NOT implementing handle_info for Phoenix.Socket.Broadcast messages
        # This should demonstrate the unobtrusive behavior - the LiveView should not crash
        # when it receives notification broadcasts it doesn't explicitly handle
      end

      # Set up user for the notification
      user = user_fixture()

      # Start the test LiveView with user_id in assigns
      {:ok, view, _html} = live_isolated(build_conn(), TestLiveView, session: %{"user_id" => user.id})

      # Verify initial state
      assert render(view) =~ "Test event"

      # Get the LiveView process PID
      lv_pid = view.pid

      # Verify the process is alive before sending the notification
      assert Process.alive?(lv_pid)

      # Simulate a notification broadcast using the actual notification system
      # This is what happens when Notifications.broadcast_user_notification/1 is called
      PetalProWeb.Endpoint.broadcast(
        PetalPro.Notifications.user_notifications_topic(user.id),
        "notifications_updated",
        %{id: 123, type: :invited_to_org}
      )

      # Give the process a moment to handle (or not handle) the broadcast
      Process.sleep(50)

      # Assert that the LiveView process remains alive and doesn't crash
      assert Process.alive?(lv_pid)
    end
  end
end
